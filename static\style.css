/* 现代化的CSS样式 - SEM关键词智能分组工具 V2.0 优化版 */

/* CSS 变量定义 */
:root {
    /* 主色调 */
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --primary-light: #dbeafe;
    --primary-dark: #1e40af;
    
    /* 次要色调 */
    --secondary-color: #10b981;
    --secondary-hover: #059669;
    --secondary-light: #d1fae5;
    
    /* 警告色 */
    --warning-color: #f59e0b;
    --warning-hover: #d97706;
    --warning-light: #fef3c7;
    
    /* 危险色 */
    --danger-color: #ef4444;
    --danger-hover: #dc2626;
    --danger-light: #fecaca;
    
    /* 中性色 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 9999px;
    
    /* 间距 */
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.75rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
    
    /* 过渡效果 */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* 基础重置 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

*::before,
*::after {
    box-sizing: border-box;
}

/* 基础样式 */
body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-800);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* 导航栏增强 */
nav {
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-md);
    position: sticky;
    top: 0;
    z-index: 1000;
}

nav h1 {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* 主要内容区域 */
main {
    background: transparent;
    padding: var(--spacing-lg);
    max-width: 1400px;
    margin: 0 auto;
}

/* 卡片样式增强 */
.card-hover,
.bg-white.rounded-lg {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    border-radius: var(--radius-lg);
}

.card-hover:hover,
.bg-white.rounded-lg:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(255, 255, 255, 0.3);
}

/* 按钮系统重构 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 14px;
    line-height: 1.5;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-height: 44px;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.btn:active {
    transform: translateY(1px);
}

/* 主要按钮 */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* 成功按钮 */
.btn-success {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-hover));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--secondary-hover), #047857);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* 警告按钮 */
.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-hover));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-warning:hover {
    background: linear-gradient(135deg, var(--warning-hover), #b45309);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* 禁用状态 */
.btn:disabled {
    background: var(--gray-300);
    color: var(--gray-500);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    opacity: 0.6;
}

/* 文件上传区域 */
#upload-area,
#baidu-upload-area,
#expand-template-upload-area,
#expand-keywords-upload-area {
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

#upload-area:hover,
#baidu-upload-area:hover,
#expand-template-upload-area:hover,
#expand-keywords-upload-area:hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-light), rgba(255, 255, 255, 0.9));
    transform: scale(1.01);
}

#upload-area.dragover,
#baidu-upload-area.dragover,
#expand-template-upload-area.dragover,
#expand-keywords-upload-area.dragover {
    border-color: var(--primary-hover);
    background: linear-gradient(135deg, var(--primary-light), #bfdbfe);
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

/* 进度条系统 */
.progress-container {
    position: relative;
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: var(--radius-full);
    transition: width var(--transition-slow);
    position: relative;
    overflow: hidden;
}

.progress-bar-primary {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
}

.progress-bar-warning {
    background: linear-gradient(90deg, var(--warning-color), var(--warning-hover));
}

.progress-bar-success {
    background: linear-gradient(90deg, var(--secondary-color), var(--secondary-hover));
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: translateX(-100%);
    animation: progress-shimmer 2s infinite;
}

@keyframes progress-shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 表单控件增强 */
.form-control {
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 14px;
    line-height: 1.5;
    transition: all var(--transition-normal);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: white;
}

.form-control::placeholder {
    color: var(--gray-400);
}

/* 复选框增强 */
.form-checkbox {
    width: 18px;
    height: 18px;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-sm);
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    background: white;
}

.form-checkbox:checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.form-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* 标签页样式增强 */
.tab-button {
    padding: var(--spacing-md) var(--spacing-xl);
    background: transparent;
    border: none;
    cursor: pointer;
    font-weight: 600;
    color: var(--gray-600);
    transition: all var(--transition-normal);
    border-bottom: 3px solid transparent;
    position: relative;
    border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.tab-button::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.tab-button:hover {
    color: var(--primary-color);
    background: rgba(59, 130, 246, 0.05);
}

.tab-button.active {
    color: var(--primary-color);
    background: rgba(59, 130, 246, 0.1);
}

.tab-button.active::before {
    transform: scaleX(1);
}

/* 标签页主题样式 */
/* 关键词分组标签 - 蓝色主题 */
.tab-keywords {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(37, 99, 235, 0.02));
}

.tab-keywords:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.05));
    color: #1d4ed8;
    transform: translateY(-1px);
}

.tab-keywords.active {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(37, 99, 235, 0.08));
    color: #1d4ed8;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.tab-keywords i {
    color: #3b82f6;
}

/* 百度转Bing标签 - 绿色主题 */
.tab-convert {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(5, 150, 105, 0.02));
}

.tab-convert:hover {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05));
    color: #047857;
    transform: translateY(-1px);
}

.tab-convert.active {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(5, 150, 105, 0.08));
    color: #047857;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
}

.tab-convert i {
    color: #10b981;
    transition: transform 0.3s ease;
}

.tab-convert:hover i {
    transform: rotate(180deg);
}

/* 必应错拼词标签 - 紫色主题 */
.tab-expander {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(124, 58, 237, 0.02));
}

.tab-expander:hover {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(124, 58, 237, 0.05));
    color: #6d28d9;
    transform: translateY(-1px);
}

.tab-expander.active {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(124, 58, 237, 0.08));
    color: #6d28d9;
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.2);
}

.tab-expander i {
    color: #8b5cf6;
    transition: transform 0.3s ease;
}

.tab-expander:hover i {
    transform: scale(1.1);
}

/* 功能介绍区域样式 */
.feature-intro {
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.feature-intro::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.8s ease;
}

.feature-intro:hover::before {
    left: 100%;
}

.feature-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    background: linear-gradient(135deg, #1f2937, #374151);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.feature-icon {
    font-size: 2.5rem;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: icon-glow 2s infinite alternate;
}

@keyframes icon-glow {
    0% { filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.3)); }
    100% { filter: drop-shadow(0 0 15px rgba(59, 130, 246, 0.6)); }
}

.feature-description {
    color: #6b7280;
    max-width: 42rem;
    margin: 0 auto;
    line-height: 1.6;
    font-size: 1.1rem;
}

/* 关键词分组功能样式 */
.feature-intro-keywords {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02), rgba(37, 99, 235, 0.01));
    border-color: rgba(59, 130, 246, 0.1);
}

.feature-intro-keywords:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(37, 99, 235, 0.02));
    border-color: rgba(59, 130, 246, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.feature-intro-keywords .feature-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 按钮增强样式 */
.btn-process {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 12px 32px;
    border-radius: 12px;
    border: none;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    transform: translateY(0);
}

.btn-process:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.btn-process:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-process::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.btn-process:hover::before {
    left: 100%;
}

.btn-process i {
    animation: spin-slow 2s infinite linear;
}

@keyframes spin-slow {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.btn-process:hover i {
    animation-duration: 0.5s;
}

/* 主要按钮样式 */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    border: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover), #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

/* 输入方式选择样式 */
.input-method-option {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #f9fafb, #f3f4f6);
    position: relative;
    overflow: hidden;
}

.input-method-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.input-method-option:hover::before {
    left: 100%;
}

.input-method-option:hover {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff, #dbeafe);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.input-method-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.option-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.option-icon {
    font-size: 18px;
    color: #6b7280;
    transition: all 0.3s ease;
}

.option-text {
    font-weight: 600;
    color: #374151;
    transition: all 0.3s ease;
}

/* 直接输入选项样式 */
.input-method-text:hover .option-icon {
    color: #3b82f6;
    animation: keyboard-bounce 0.6s ease;
}

@keyframes keyboard-bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
}

.input-method-text input:checked + .option-content {
    color: #1d4ed8;
}

.input-method-text input:checked + .option-content .option-icon {
    color: #3b82f6;
}

.input-method-text input:checked ~ .input-method-option {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
}

/* 文件上传选项样式 */
.input-method-file:hover .option-icon {
    color: #10b981;
    animation: upload-bounce 0.6s ease;
}

@keyframes upload-bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
}

.input-method-file input:checked + .option-content {
    color: #047857;
}

.input-method-file input:checked + .option-content .option-icon {
    color: #10b981;
}

.input-method-file input:checked ~ .input-method-option {
    border-color: #10b981;
    background: linear-gradient(135deg, #d1fae5, #a7f3d0);
}

/* 选中状态样式 */
.input-method-option:has(input:checked) {
    border-width: 3px;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

/* 统计卡片增强 */
.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: var(--spacing-xs);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 分组卡片增强 */
.group-card {
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
}

.group-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.group-header {
    background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
}

.group-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
}

.group-header:hover {
    background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
}

.group-content {
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.group-content.expanded {
    max-height: 500px;
    padding: var(--spacing-lg);
    }
    
    .group-keywords {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xs);
    max-height: 350px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--gray-300) transparent;
}

.keyword-item {
    background: var(--gray-50);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-fast);
    font-size: 14px;
    cursor: pointer;
}

.keyword-item:hover {
    background: var(--primary-light);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* 动画增强 */
@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(20px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

@keyframes slideIn {
    from { 
        transform: translateX(-100%); 
        opacity: 0; 
    }
    to { 
        transform: translateX(0); 
        opacity: 1; 
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { 
        transform: translate3d(0, 0, 0); 
    }
    40%, 43% { 
        transform: translate3d(0, -8px, 0); 
    }
    70% { 
        transform: translate3d(0, -4px, 0); 
    }
    90% { 
        transform: translate3d(0, -2px, 0); 
    }
}

@keyframes pulse {
    0% { 
        transform: scale(1); 
    }
    50% { 
        transform: scale(1.05); 
    }
    100% { 
        transform: scale(1); 
    }
}

@keyframes shake {
    0%, 100% { 
        transform: translateX(0); 
    }
    25% { 
        transform: translateX(-5px); 
    }
    75% { 
        transform: translateX(5px); 
    }
}

/* 动画类 */
.animate-fadeIn {
    animation: fadeIn 0.6s ease-out;
}

.animate-slideIn {
    animation: slideIn 0.5s ease-out;
}

.animate-bounce {
    animation: bounce 1s ease-in-out;
}

.animate-pulse {
    animation: pulse 2s ease-in-out;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

/* 加载状态 */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transform: translateX(-100%);
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 300px;
    margin: var(--spacing-xl) 0;
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 工具提示 */
.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 12px;
    pointer-events: none;
    transform: translate(-50%, -100%);
    margin-top: -8px;
    opacity: 0;
    transition: opacity var(--transition-normal);
    backdrop-filter: blur(10px);
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
}

.tooltip.show {
    opacity: 1;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius-sm);
    transition: background var(--transition-normal);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    main {
        padding: var(--spacing-md);
    }
    
    .grid-cols-4 {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    :root {
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 0.75rem;
        --spacing-lg: 1rem;
        --spacing-xl: 1.5rem;
        --spacing-2xl: 2rem;
    }
    
    main {
        padding: var(--spacing-sm);
    }
    
    .grid-cols-4,
    .grid-cols-3 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .group-keywords {
        grid-template-columns: 1fr;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 13px;
    }
    
    .tab-button {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 14px;
    }
}

@media (max-width: 640px) {
    .grid-cols-2 {
        grid-template-columns: 1fr;
    }
    
    .stat-number {
        font-size: 1.75rem;
    }
    
    .group-header {
        padding: var(--spacing-md);
    }
    
    .group-content.expanded {
        padding: var(--spacing-md);
    }
}

/* 性能优化 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
    :root {
        --gray-300: #000;
        --gray-600: #000;
        --primary-color: #0000ff;
        --secondary-color: #008000;
    }
    
    .form-control {
        border: 2px solid #000;
    }
    
    .btn-primary {
        background: #000;
        color: #fff;
    }
    
    .group-card {
        border: 2px solid #000;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-50: #1f2937;
        --gray-100: #374151;
        --gray-200: #4b5563;
        --gray-300: #6b7280;
        --gray-400: #9ca3af;
        --gray-500: #d1d5db;
        --gray-600: #e5e7eb;
        --gray-700: #f3f4f6;
        --gray-800: #f9fafb;
        --gray-900: #ffffff;
    }
    
    body {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        color: var(--gray-800);
    }
    
    .bg-white {
        background: rgba(31, 41, 55, 0.95) !important;
        color: var(--gray-800);
    }
    
    nav {
        background: rgba(31, 41, 55, 0.95);
    }
    
    .form-control {
        background: rgba(75, 85, 99, 0.8);
        border-color: var(--gray-300);
        color: var(--gray-800);
    }
    
    .keyword-item {
        background: var(--gray-200);
        border-color: var(--gray-300);
        color: var(--gray-700);
    }
    
    .group-header {
        background: linear-gradient(135deg, var(--gray-200), var(--gray-100));
    }
}

/* 打印样式 */
@media print {
    body {
        background: white;
        color: black;
    }
    
    .no-print {
        display: none !important;
    }
    
    .group-content {
        max-height: none !important;
        padding: var(--spacing-lg) !important;
    }
    
    .btn {
        border: 1px solid #000;
        background: white;
        color: black;
    }
}

/* 特定功能样式 */
.tab-content {
    animation: fadeIn 0.3s ease-in-out;
}

.hidden {
    display: none !important;
}

/* 导出按钮特殊样式 */
#export-complete-btn,
#baidu-export-btn,
#expand-export-button {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-hover)) !important;
    border: none !important;
    color: white !important;
    padding: var(--spacing-md) var(--spacing-xl) !important;
    border-radius: var(--radius-md) !important;
    cursor: pointer !important;
    transition: all var(--transition-normal) !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    box-shadow: var(--shadow-lg) !important;
    position: relative !important;
    overflow: hidden !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 48px !important;
    text-decoration: none !important;
    gap: var(--spacing-xs) !important;
}

#export-complete-btn:hover,
#baidu-export-btn:hover,
#expand-export-button:hover {
    background: linear-gradient(135deg, var(--secondary-hover), #047857) !important;
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-xl) !important;
}

#export-complete-btn:active,
#baidu-export-btn:active,
#expand-export-button:active {
    transform: translateY(0) !important;
    box-shadow: var(--shadow-md) !important;
}

/* 开始按钮样式 */
#process-btn,
#baidu-convert-btn,
#expand-start-button {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-hover)) !important;
    border: none !important;
    color: white !important;
    padding: var(--spacing-md) var(--spacing-xl) !important;
    border-radius: var(--radius-md) !important;
    cursor: pointer !important;
    transition: all var(--transition-normal) !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    box-shadow: var(--shadow-lg) !important;
    position: relative !important;
    overflow: hidden !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 48px !important;
    text-decoration: none !important;
    gap: var(--spacing-xs) !important;
}

#process-btn:hover,
#baidu-convert-btn:hover,
#expand-start-button:hover {
    background: linear-gradient(135deg, var(--secondary-hover), #047857) !important;
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-xl) !important;
}

#process-btn:active,
#baidu-convert-btn:active,
#expand-start-button:active {
    transform: translateY(0) !important;
    box-shadow: var(--shadow-md) !important;
}

#expand-start-button:disabled {
    background: linear-gradient(135deg, var(--gray-300), var(--gray-400)) !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
    opacity: 0.6 !important;
}

/* 进度条特定样式 */
#progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover)) !important;
}

#baidu-progress-bar {
    background: linear-gradient(90deg, var(--warning-color), var(--warning-hover)) !important;
}

#expand-progress-bar {
    background: linear-gradient(90deg, var(--secondary-color), var(--secondary-hover)) !important;
}

/* 配置摘要增强 */
#expand-config-summary {
    background: linear-gradient(135deg, var(--warning-light), #fed7aa) !important;
    border: 2px solid var(--warning-color) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--spacing-lg) !important;
    margin-bottom: var(--spacing-lg) !important;
    box-shadow: var(--shadow-md) !important;
    transition: all var(--transition-normal) !important;
}

#expand-config-summary:hover {
    transform: translateY(-1px) !important;
    box-shadow: var(--shadow-lg) !important;
}

#expand-summary-text {
    color: var(--warning-hover) !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
}

/* 错误提示增强 */
.error-container {
    background: linear-gradient(135deg, var(--danger-light), #fecaca) !important;
    border: 2px solid var(--danger-color) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--spacing-xl) !important;
    margin: var(--spacing-lg) 0 !important;
    box-shadow: var(--shadow-md) !important;
    animation: shake 0.5s ease-in-out !important;
}

/* 确保所有交互元素都有足够的点击区域 */
button,
.btn,
input[type="checkbox"],
input[type="radio"] {
    min-height: 20px;
    min-width: 20px;
}

/* 焦点样式统一 */
button:focus,
.btn:focus,
input:focus,
textarea:focus,
select:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* 性能优化 - 硬件加速 */
.card-hover,
.btn,
.group-card,
.tab-button,
.stat-card {
    will-change: transform;
    transform: translateZ(0);
}

/* 新增：关键词输入界面样式 */
.form-radio {
    appearance: none;
    background-color: #fff;
    border: 2px solid var(--gray-300);
    border-radius: 50%;
    width: 16px;
    height: 16px;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
}

.form-radio:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-radio:checked::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    background-color: white;
    border-radius: 50%;
}

.form-radio:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* 关键词文本区域样式 */
#keywords-textarea {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    line-height: 1.5;
    resize: vertical;
    min-height: 200px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

#keywords-textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 关键词计数样式 */
#keywords-count {
    font-weight: 600;
    color: var(--primary-color);
}

/* 输入方式切换动画 */
#text-input-area,
#file-input-area {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

#text-input-area.hidden,
#file-input-area.hidden {
    opacity: 0;
    transform: translateY(-10px);
    pointer-events: none;
}

/* 示例关键词按钮样式 */
#sample-keywords-btn {
    position: relative;
    overflow: hidden;
}

#sample-keywords-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

#sample-keywords-btn:hover::before {
    left: 100%;
}

/* 处理按钮增强样式 */
#process-text-btn {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--secondary-color), #059669);
    border: none;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

#process-text-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--secondary-hover), #047857);
}

#process-text-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

#process-text-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

#process-text-btn:hover::before {
    left: 100%;
}

/* 导航链接区别样式 */
.nav-link {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.nav-link:hover::before {
    left: 100%;
}

/* 关键词拓词 - 紫色主题 */
.nav-link-generate {
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    color: white;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.nav-link-generate:hover {
    background: linear-gradient(135deg, #7c3aed, #9333ea);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
    color: white;
}

.nav-link-generate i {
    color: #fbbf24;
    text-shadow: 0 0 8px rgba(251, 191, 36, 0.5);
}

/* 创意生成 - 橙色主题 */
.nav-link-creative {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.nav-link-creative:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
    color: white;
}

.nav-link-creative i {
    color: #fef3c7;
    text-shadow: 0 0 8px rgba(254, 243, 199, 0.8);
    animation: pulse-light 2s infinite;
}

@keyframes pulse-light {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 关键词查询 - 蓝色主题 */
.nav-link-search {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.nav-link-search:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    color: white;
}

.nav-link-search i {
    color: #dbeafe;
    animation: search-pulse 1.5s infinite;
}

@keyframes search-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* 导航链接激活状态 */
.nav-link:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 导航链接焦点状态 */
.nav-link:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* 响应式设计优化 */
@media (max-width: 768px) {
    #keywords-textarea {
        height: 150px;
        font-size: 14px;
    }

    .form-radio {
        width: 18px;
        height: 18px;
    }

    #process-text-btn {
        width: 100%;
        padding: 12px;
    }

    .nav-link {
        padding: 6px 12px;
        font-size: 13px;
        margin: 2px;
    }

    .nav-link i {
        margin-right: 4px;
    }
}

@media (max-width: 480px) {
    .nav-link {
        padding: 8px;
        font-size: 12px;
        flex-direction: column;
        text-align: center;
        min-width: 80px;
    }

    .nav-link i {
        margin-right: 0;
        margin-bottom: 2px;
        font-size: 16px;
    }
}