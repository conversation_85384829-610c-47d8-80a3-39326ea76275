<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEM关键词智能分组工具 V2.0</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <i class="fas fa-search text-blue-600 text-2xl mr-3"></i>
                    <h1 class="text-xl font-bold text-gray-900">SEM关键词智能分组工具</h1>
                    <span class="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">V2.0</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-600">
                        <i class="fas fa-bolt mr-1"></i>
                        FastAPI版本
                    </div>
                    <a href="/custom-keywords" class="nav-link nav-link-generate">
                        <i class="fas fa-magic mr-1"></i>
                        关键词拓词
                    </a>
                    <a href="/ad-tips" class="nav-link nav-link-creative">
                        <i class="fas fa-lightbulb mr-1"></i>
                        创意生成
                    </a>
                    <a href="/keyword-sync" class="nav-link nav-link-search">
                        <i class="fas fa-search mr-1"></i>
                        关键词查询
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- 标签页导航 -->
        <div class="bg-white rounded-lg shadow-md mb-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex">
                    <button id="tab-keywords" class="tab-button tab-keywords active py-4 px-6 border-b-2 border-blue-500 text-blue-600 font-semibold">
                        <i class="fas fa-layer-group mr-2"></i>
                        关键词分组
                    </button>
                    <button id="tab-baidu-convert" class="tab-button tab-convert py-4 px-6 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-semibold">
                        <i class="fas fa-exchange-alt mr-2"></i>
                        百度转Bing
                    </button>
                    <button id="tab-keyword-expander" class="tab-button tab-expander py-4 px-6 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-semibold">
                        <i class="fas fa-expand-arrows-alt mr-2"></i>
                        必应错拼词
                    </button>
                </nav>
            </div>
        </div>

        <!-- 标签页内容 -->
        <div id="tab-content-keywords" class="tab-content">
            <!-- 功能介绍 -->
            <div class="feature-intro feature-intro-keywords bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="text-center">
                    <h2 class="feature-title">
                        <i class="fas fa-layer-group feature-icon"></i>
                        关键词智能分组
                    </h2>
                    <p class="feature-description">
                        专业的SEM关键词分组工具，采用智能算法自动识别品牌词、下载词、功能词等，
                        大幅提升关键词管理效率，助力广告投放精准化。
                    </p>
                </div>
            </div>

            <!-- 快捷功能卡片 -->
            <div class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg shadow-md p-6 mb-6 text-white">
                <div class="text-center">
                    <h3 class="text-xl font-bold mb-2">🎯 新功能推荐</h3>
                    <p class="mb-4">
                        想要快速生成大量长尾关键词？试试我们的自定义关键词生成器！
                        只需配置软件名称和别名，即可智能生成数千个高质量关键词。
                    </p>
                    <a href="/custom-keywords" class="inline-block bg-white text-purple-600 font-semibold py-2 px-6 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                        <i class="fas fa-magic mr-2"></i>立即体验
                    </a>
                </div>
            </div>

            <!-- 配置面板 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <!-- 基础配置 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-cog text-blue-600 mr-2"></i>基础配置
                    </h3>
                    
                    <div class="space-y-4">
                        <!-- 快捷预设 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">快捷预设</label>
                            <select id="preset-select" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="recommended">推荐配置（智能+n-gram）</option>
                                <option value="custom">自定义配置</option>
                                <option value="fast">快速配置（传统算法）</option>
                                <option value="precise">精准配置（仅智能算法）</option>
                            </select>
                        </div>

                        <!-- 品牌词算法 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">品牌词算法</label>
                            <select id="brand-algorithm" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="智能词组识别">智能词组识别</option>
                                <option value="传统单词识别">传统单词识别</option>
                            </select>
                        </div>

                        <!-- 高级选项 -->
                        <div class="space-y-3">
                            <label class="flex items-center">
                                <input type="checkbox" id="enable-ngram" class="form-checkbox h-4 w-4 text-blue-600 rounded focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">启用n-gram分析</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="show-filtered" class="form-checkbox h-4 w-4 text-blue-600 rounded focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">显示过滤词</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="enable-core-recommendation" class="form-checkbox h-4 w-4 text-blue-600 rounded focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">核心词推荐</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 核心品牌词 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-bullseye text-red-600 mr-2"></i>核心品牌词
                    </h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">识别方式</label>
                            <select id="core-brand-option" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="自动识别">自动识别</option>
                                <option value="手动指定">手动指定</option>
                            </select>
                        </div>

                        <div id="manual-core-brand-container" class="hidden">
                            <label class="block text-sm font-medium text-gray-700 mb-2">手动指定品牌词</label>
                            <input type="text" id="manual-core-brand" placeholder="例如: potplayer" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
                            <div class="flex items-center">
                                <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                                <span class="text-sm text-blue-800">
                                    品牌词将用于创建核心品牌词组，影响整体分组效果
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 否定词设置 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-ban text-red-600 mr-2"></i>否定词设置
                    </h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">否定词选项</label>
                            <select id="negative-words-option" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="default">使用默认否定词</option>
                                <option value="modify">在默认基础上修改</option>
                                <option value="custom">完全自定义</option>
                                <option value="none">不使用否定词</option>
                            </select>
                        </div>

                        <div id="negative-words-container" class="hidden">
                            <label class="block text-sm font-medium text-gray-700 mb-2">否定词列表</label>
                            <textarea id="negative-words" rows="6" placeholder="每行一个否定词..." 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                            <div class="flex justify-between items-center mt-2">
                                <button id="load-default-btn" class="text-sm text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-download mr-1"></i>加载默认
                                </button>
                                <span id="negative-words-count" class="text-sm text-gray-500">0 个否定词</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 关键词输入区域 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-keyboard text-green-600 mr-2"></i>关键词输入
                </h3>

                <!-- 输入方式选择 -->
                <div class="mb-6">
                    <div class="flex space-x-4">
                        <label class="input-method-option input-method-text">
                            <input type="radio" name="input-method" value="text" class="form-radio" checked>
                            <span class="option-content">
                                <i class="fas fa-keyboard option-icon"></i>
                                <span class="option-text">直接输入</span>
                            </span>
                        </label>
                        <label class="input-method-option input-method-file">
                            <input type="radio" name="input-method" value="file" class="form-radio">
                            <span class="option-content">
                                <i class="fas fa-upload option-icon"></i>
                                <span class="option-text">文件上传</span>
                            </span>
                        </label>
                    </div>
                </div>

                <!-- 直接输入区域 -->
                <div id="text-input-area" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            输入关键词（每行一个）
                        </label>
                        <textarea id="keywords-textarea"
                                  class="w-full h-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-vertical"
                                  placeholder="请输入关键词，每行一个&#10;例如：&#10;potplayer&#10;potplayer下载&#10;potplayer播放器&#10;potplayer官网">potplayer
potplayer下载
potplayer播放器
potplayer官网
potplayer安装
potplayer中文版</textarea>
                    </div>

                    <div class="flex justify-between items-center">
                        <div class="text-sm text-gray-600">
                            <span id="keywords-count">6</span> 个关键词
                        </div>
                        <div class="flex space-x-2">
                            <button id="clear-keywords-btn" class="text-sm text-gray-500 hover:text-gray-700">
                                <i class="fas fa-trash mr-1"></i>清空
                            </button>
                            <button id="sample-keywords-btn" class="text-sm text-blue-600 hover:text-blue-800">
                                <i class="fas fa-magic mr-1"></i>示例关键词
                            </button>
                        </div>
                    </div>

                    <div class="flex justify-center">
                        <button id="process-text-btn" class="btn-primary btn-process px-6 py-2 rounded-md bg-blue-600 text-white ">
                            <i class="fas fa-cogs mr-2"></i>开始智能分组
                        </button>
                    </div>
                </div>

                <!-- 文件上传区域 -->
                <div id="file-input-area" class="hidden">
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors duration-200"
                         id="upload-area">
                        <div id="upload-default">
                            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                            <p class="text-lg text-gray-600 mb-2">拖拽文件到此处或点击上传</p>
                            <p class="text-sm text-gray-500 mb-4">支持 TXT、CSV、XLSX 格式，最大 200MB</p>
                            <input type="file" id="file-input" accept=".txt,.csv,.xlsx" class="hidden">
                            <button id="upload-btn" class="btn btn-primary">
                                <i class="fas fa-folder-open"></i>选择文件
                            </button>
                        </div>

                        <div id="upload-success" class="hidden">
                            <i class="fas fa-check-circle text-4xl text-green-600 mb-4"></i>
                            <p class="text-lg text-green-600 mb-2">文件上传成功</p>
                            <p id="file-info" class="text-sm text-gray-600 mb-4"></p>
                            <div class="flex justify-center space-x-4">
                                <button id="process-btn" class="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 transition-colors">
                                    <i class="fas fa-cogs mr-2"></i>开始分组
                                </button>
                                <button id="reupload-btn" class="bg-gray-600 text-white px-6 py-2 rounded-md hover:bg-gray-700 transition-colors">
                                    <i class="fas fa-redo mr-2"></i>重新上传
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 处理进度 -->
            <div id="progress-container" class="bg-white rounded-lg shadow-md p-6 mb-6 hidden">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-spinner fa-spin text-blue-600 mr-2"></i>处理进度
                </h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span id="progress-step" class="text-sm font-medium text-gray-700">准备开始...</span>
                        <span id="progress-percent" class="text-sm text-gray-500">0%</span>
                    </div>
                    
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    
                    <p id="progress-message" class="text-sm text-gray-600">等待处理...</p>
                    
                    <div id="progress-time" class="text-xs text-gray-500">
                        <i class="fas fa-clock mr-1"></i>
                        <span id="elapsed-time">00:00</span>
                    </div>
                </div>
            </div>

            <!-- 结果展示区域 -->
            <div id="results-container" class="hidden">
                <!-- 统计概览 -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-chart-bar text-purple-600 mr-2"></i>分组统计
                    </h3>
                    
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="stat-card">
                            <div class="stat-number" id="total-keywords">0</div>
                            <div class="stat-label">总关键词</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="final-count">0</div>
                            <div class="stat-label">最终数量</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="group-count">0</div>
                            <div class="stat-label">分组数量</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="filtered-count">0</div>
                            <div class="stat-label">过滤数量</div>
                        </div>
                    </div>
                    
                    <div id="core-brand-display" class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md hidden">
                        <div class="flex items-center">
                            <i class="fas fa-star text-yellow-600 mr-2"></i>
                            <span class="text-sm font-medium text-yellow-800">核心品牌词:</span>
                            <span id="core-brand-text" class="ml-2 text-sm text-yellow-900 font-semibold"></span>
                        </div>
                    </div>
                </div>

                <!-- 分组结果 -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-layer-group text-indigo-600 mr-2"></i>分组结果
                    </h3>
                    
                    <div id="groups-container" class="space-y-4">
                        <!-- 分组将在这里动态生成 -->
                    </div>
                </div>

                <!-- 图表分析 -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-chart-pie text-pink-600 mr-2"></i>分组分析
                    </h3>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <canvas id="pie-chart"></canvas>
                        </div>
                        <div>
                            <canvas id="bar-chart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 导出功能 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-download text-teal-600 mr-2"></i>导出数据
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 mb-2">完整分组数据</h4>
                            <p class="text-sm text-gray-600 mb-3">包含所有关键词的详细分组信息</p>
                            <button id="export-complete-btn" class="w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 transition-colors">
                                <i class="fas fa-file-csv mr-2"></i>导出完整数据
                            </button>
                        </div>
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 mb-2">推广计划数据</h4>
                            <p class="text-sm text-gray-600 mb-3">每个分组的代表性关键词和推广建议</p>
                            <button id="export-campaign-btn" class="w-full bg-green-600 text-white py-2 rounded-md hover:bg-green-700 transition-colors">
                                <i class="fas fa-file-excel mr-2"></i>导出推广计划
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 错误提示 -->
            <div id="error-container" class="bg-red-50 border border-red-200 rounded-lg p-6 hidden">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle text-red-600 mr-3"></i>
                    <div>
                        <h3 class="text-lg font-semibold text-red-800">处理失败</h3>
                        <p id="error-message" class="text-sm text-red-700 mt-1"></p>
                    </div>
                </div>
                <button id="retry-btn" class="mt-4 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors">
                    <i class="fas fa-redo mr-2"></i>重新处理
                </button>
            </div>
        </div>

        <div id="tab-content-baidu-convert" class="tab-content hidden">
            <!-- 百度转Bing功能介绍 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="text-center">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">🔄 百度SEM转Bing Ads</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto">
                        将百度SEM关键词文件转换为Bing Ads上传格式，支持匹配模式转换、URL解析和数据清洗，
                        让您轻松将百度广告迁移到Bing平台。
                    </p>
                </div>
            </div>

            <!-- 转换配置和模板信息 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- 转换设置 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-cogs text-blue-600 mr-2"></i>转换设置
                    </h3>
                    
                    <div class="space-y-4">
                        <label class="flex items-center">
                            <input type="checkbox" id="baidu-enable-url-parsing" class="form-checkbox h-4 w-4 text-blue-600 rounded focus:ring-blue-500" checked>
                            <span class="ml-2 text-sm text-gray-700">🔗 启用URL解析</span>
                        </label>
                        <p class="text-xs text-gray-500 ml-6">解析百度链接中的目标URL</p>
                        
                        <label class="flex items-center">
                            <input type="checkbox" id="baidu-enable-data-cleaning" class="form-checkbox h-4 w-4 text-blue-600 rounded focus:ring-blue-500" checked>
                            <span class="ml-2 text-sm text-gray-700">🧹 启用数据清洗</span>
                        </label>
                        <p class="text-xs text-gray-500 ml-6">去除空行、重复数据和无效内容</p>
                        
                        <label class="flex items-center">
                            <input type="checkbox" id="baidu-preserve-original-data" class="form-checkbox h-4 w-4 text-blue-600 rounded focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">💾 保留原始数据</span>
                        </label>
                        <p class="text-xs text-gray-500 ml-6">在转换结果中保留原始百度数据列</p>
                    </div>
                </div>

                <!-- 模板信息 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-file-alt text-green-600 mr-2"></i>模板信息
                    </h3>
                    
                    <div id="baidu-template-info" class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">上传模板：</span>
                            <span id="baidu-upload-template" class="text-sm font-medium text-gray-900">加载中...</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">导出模板：</span>
                            <span id="baidu-export-template" class="text-sm font-medium text-gray-900">加载中...</span>
                        </div>
                    </div>
                    
                    <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                            <span class="text-sm text-blue-800">
                                支持CSV和Excel格式，最大文件200MB
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文件上传区域 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-upload text-orange-600 mr-2"></i>百度SEM文件上传
                </h3>
                
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors duration-200" 
                     id="baidu-upload-area">
                    <div id="baidu-upload-default">
                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                        <p class="text-lg text-gray-600 mb-2">拖拽百度SEM文件到此处或点击上传</p>
                        <p class="text-sm text-gray-500 mb-4">支持 CSV、XLSX 格式，最大 200MB</p>
                        <input type="file" id="baidu-file-input" accept=".csv,.xlsx" class="hidden">
                        <button id="baidu-upload-btn" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors">
                            <i class="fas fa-folder-open mr-2"></i>选择文件
                        </button>
                    </div>
                    
                    <div id="baidu-upload-success" class="hidden">
                        <i class="fas fa-check-circle text-4xl text-green-600 mb-4"></i>
                        <p class="text-lg text-green-600 mb-2">文件上传成功</p>
                        <p id="baidu-file-info" class="text-sm text-gray-600 mb-4"></p>
                        
                        <!-- 文件预览 -->
                        <div id="baidu-file-preview" class="hidden mb-4">
                            <div class="bg-gray-50 rounded-lg p-4 text-left">
                                <h4 class="font-medium text-gray-900 mb-2">📋 文件预览</h4>
                                <div id="baidu-preview-info" class="text-sm text-gray-600 mb-2"></div>
                                <div id="baidu-preview-table" class="overflow-x-auto max-h-40 border border-gray-200 rounded">
                                    <!-- 预览表格将在这里显示 -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex justify-center space-x-4">
                            <button id="baidu-convert-btn" class="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 transition-colors">
                                <i class="fas fa-exchange-alt mr-2"></i>开始转换
                            </button>
                            <button id="baidu-preview-btn" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors">
                                <i class="fas fa-eye mr-2"></i>预览文件
                            </button>
                            <button id="baidu-reupload-btn" class="bg-gray-600 text-white px-6 py-2 rounded-md hover:bg-gray-700 transition-colors">
                                <i class="fas fa-redo mr-2"></i>重新上传
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 转换进度 -->
            <div id="baidu-progress-container" class="bg-white rounded-lg shadow-md p-6 mb-6 hidden">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-spinner fa-spin text-blue-600 mr-2"></i>转换进度
                </h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span id="baidu-progress-step" class="text-sm font-medium text-gray-700">准备开始...</span>
                        <span id="baidu-progress-percent" class="text-sm text-gray-500">0%</span>
                    </div>
                    
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="baidu-progress-bar" class="bg-orange-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    
                    <p id="baidu-progress-message" class="text-sm text-gray-600">等待转换...</p>
                    
                    <div id="baidu-progress-time" class="text-xs text-gray-500">
                        <i class="fas fa-clock mr-1"></i>
                        <span id="baidu-elapsed-time">00:00</span>
                    </div>
                </div>
            </div>

            <!-- 转换结果展示 -->
            <div id="baidu-results-container" class="hidden">
                <!-- 转换统计 -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-chart-bar text-purple-600 mr-2"></i>转换统计
                    </h3>
                    
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="stat-card">
                            <div class="stat-number" id="baidu-original-rows">0</div>
                            <div class="stat-label">原始数据</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="baidu-converted-rows">0</div>
                            <div class="stat-label">转换后</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="baidu-conversion-rate">0%</div>
                            <div class="stat-label">转换率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="baidu-columns-count">0</div>
                            <div class="stat-label">数据列数</div>
                        </div>
                    </div>
                </div>

                <!-- 转换结果预览 -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-table text-indigo-600 mr-2"></i>转换结果预览
                    </h3>
                    
                    <div id="baidu-result-preview" class="overflow-x-auto">
                        <!-- 结果表格将在这里显示 -->
                    </div>
                </div>

                <!-- 导出功能 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-download text-teal-600 mr-2"></i>导出转换结果
                    </h3>
                    
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium text-gray-900 mb-1">Bing Ads 关键词文件</h4>
                            <p class="text-sm text-gray-600">Excel格式，兼容Bing Ads上传要求</p>
                        </div>
                        <button id="baidu-export-btn" class="bg-teal-600 text-white px-6 py-2 rounded-md hover:bg-teal-700 transition-colors">
                            <i class="fas fa-file-excel mr-2"></i>导出Excel文件
                        </button>
                    </div>
                </div>
            </div>

            <!-- 错误提示 -->
            <div id="baidu-error-container" class="bg-red-50 border border-red-200 rounded-lg p-6 hidden">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle text-red-600 mr-3"></i>
                    <div>
                        <h3 class="text-lg font-semibold text-red-800">转换失败</h3>
                        <p id="baidu-error-message" class="text-sm text-red-700 mt-1"></p>
                    </div>
                </div>
                <button id="baidu-retry-btn" class="mt-4 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors">
                    <i class="fas fa-redo mr-2"></i>重新转换
                </button>
            </div>
        </div>

        <div id="tab-content-keyword-expander" class="tab-content hidden">
            <!-- 必应错拼词功能介绍 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="text-center">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">🚀 必应错拼词</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto">
                        根据基础关键词和模板文件生成扩展关键词，专门用于错拼词等变体生成，
                        帮助扩大关键词覆盖范围，提高广告触达率。
                    </p>
                </div>
            </div>

            <!-- 配置面板 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- 基础配置 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-cog text-orange-600 mr-2"></i>基础配置
                    </h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">推广计划名称</label>
                            <input type="text" id="expand-campaign-name" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                                   value="第三方软件-potplayer">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">广告组名称</label>
                            <input type="text" id="expand-ad-group-name" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                                   value="错拼词">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">匹配模式</label>
                            <select id="expand-match-type" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                                <option value="Exact">精确匹配</option>
                                <option value="Phrase">短语匹配</option>
                                <option value="Broad">广泛匹配</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">出价</label>
                            <input type="text" id="expand-bid-price" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                                   value="0.39">
                        </div>
                    </div>
                </div>

                <!-- URL和模板设置 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-link text-orange-600 mr-2"></i>URL和模板设置
                    </h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">最终访问网址</label>
                            <input type="text" id="expand-final-url" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                                   value="https://sem.duba.net/sem/dseek/f255.html?sfrom=196&TFT=16&keyID=144553">
                        </div>
                        
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" id="expand-use-custom-template" class="mr-2">
                                <span class="text-sm font-medium text-gray-700">使用自定义模板</span>
                            </label>
                        </div>
                        
                        <!-- 自定义模板上传 -->
                        <div id="expand-custom-template-section" class="hidden">
                            <label class="block text-sm font-medium text-gray-700 mb-2">上传模板文件</label>
                            <div class="relative">
                                <input type="file" id="expand-template-file" accept=".xlsx,.xls" class="hidden">
                                <div id="expand-template-upload-area" 
                                     class="border-2 border-dashed border-orange-300 rounded-lg p-4 text-center cursor-pointer hover:border-orange-400 transition-colors">
                                    <i class="fas fa-upload text-orange-400 text-2xl mb-2"></i>
                                    <p class="text-sm text-gray-600">点击或拖拽上传Excel模板文件</p>
                                    <p class="text-xs text-gray-500 mt-1">必须包含'keys'列</p>
                                </div>
                            </div>
                            <div id="expand-template-status" class="mt-2"></div>
                        </div>
                        
                        <!-- 默认模板状态 -->
                        <div id="expand-default-template-status" class="text-sm">
                            <div class="flex items-center text-green-600">
                                <i class="fas fa-check-circle mr-2"></i>
                                <span>使用默认模板：codigo.xlsx</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 基础关键词输入 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-keyboard text-orange-600 mr-2"></i>基础关键词
                </h3>
                
                <!-- 输入方式选择 -->
                <div class="mb-4">
                    <div class="flex space-x-4">
                        <label class="flex items-center">
                            <input type="radio" name="expand-input-method" value="manual" class="mr-2" checked>
                            <span class="text-sm font-medium text-gray-700">手动输入</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="expand-input-method" value="file" class="mr-2">
                            <span class="text-sm font-medium text-gray-700">文件上传</span>
                        </label>
                    </div>
                </div>
                
                <!-- 手动输入区域 -->
                <div id="expand-manual-input" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            输入基础关键词（一行一个）
                        </label>
                        <textarea id="expand-keywords-text" 
                                  class="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                                  placeholder="请输入基础关键词，每行一个&#10;例如：&#10;potplayer&#10;potplayer播放器&#10;potplayer下载">potplayer
potplayer播放器
potplayer下载</textarea>
                    </div>
                    <div id="expand-manual-keywords-count" class="text-sm text-gray-600"></div>
                </div>
                
                <!-- 文件上传区域 -->
                <div id="expand-file-input" class="hidden space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">上传关键词文件</label>
                        <div class="relative">
                            <input type="file" id="expand-keywords-file" accept=".txt,.csv,.xlsx,.xls" class="hidden">
                            <div id="expand-keywords-upload-area" 
                                 class="border-2 border-dashed border-orange-300 rounded-lg p-6 text-center cursor-pointer hover:border-orange-400 transition-colors">
                                <i class="fas fa-file-upload text-orange-400 text-3xl mb-3"></i>
                                <p class="text-gray-600 mb-2">点击或拖拽上传关键词文件</p>
                                <p class="text-sm text-gray-500">支持 TXT、CSV、Excel 格式</p>
                            </div>
                        </div>
                        <div id="expand-keywords-file-status" class="mt-2"></div>
                    </div>
                </div>
            </div>

            <!-- 开始扩展按钮 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-rocket text-blue-600 mr-2"></i>开始扩展
                </h3>
                
                <!-- 配置摘要 -->
                <div id="expand-config-summary" class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
                    <div class="text-sm text-orange-800">
                        <span class="font-medium">配置摘要：</span>
                        <span id="expand-summary-text">请完成配置</span>
                    </div>
                </div>
                
                <button id="expand-start-button" 
                        class="w-full bg-green-600 text-white py-2 rounded-md hover:bg-green-700 transition-colors"
                        disabled>
                    <i class="fas fa-rocket mr-2"></i>
                    <span id="expand-button-text">开始扩展关键词</span>
                </button>
            </div>

            <!-- 处理进度 -->
            <div id="expand-progress-section" class="bg-white rounded-lg shadow-md p-6 mb-6 hidden">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-tasks text-orange-600 mr-2"></i>扩展进度
                </h3>
                
                <div class="space-y-4">
                    <div>
                        <div class="flex justify-between text-sm text-gray-600 mb-1">
                            <span id="expand-progress-step">准备中...</span>
                            <span id="expand-progress-percentage">0%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div id="expand-progress-bar" class="bg-orange-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div id="expand-progress-message" class="text-sm text-gray-600">
                        等待开始...
                    </div>
                    
                    <div id="expand-progress-time" class="text-xs text-gray-500">
                        ⏱️ 已用时：0秒
                    </div>
                </div>
            </div>

            <!-- 扩展结果 -->
            <div id="expand-results-section" class="bg-white rounded-lg shadow-md p-6 hidden">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-chart-line text-orange-600 mr-2"></i>扩展结果
                </h3>
                
                <!-- 统计信息 -->
                <div id="expand-statistics" class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div class="stat-card">
                        <div class="stat-number" id="expand-stat-original">0</div>
                        <div class="stat-label">基础关键词</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="expand-stat-expanded">0</div>
                        <div class="stat-label">扩展关键词</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="expand-stat-final">0</div>
                        <div class="stat-label">最终行数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="expand-stat-rate">0x</div>
                        <div class="stat-label">扩展倍数</div>
                    </div>
                </div>
                
                <!-- 结果预览 -->
                <div class="mb-6">
                    <h4 class="text-md font-semibold text-gray-800 mb-3">
                        <i class="fas fa-table text-gray-600 mr-2"></i>结果预览
                    </h4>
                    <div class="overflow-x-auto">
                        <table id="expand-results-table" class="min-w-full bg-white border border-gray-200">
                            <thead class="bg-gray-50">
                                <!-- 表头将通过JavaScript动态生成 -->
                            </thead>
                            <tbody>
                                <!-- 表格内容将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 导出按钮 -->
                <div class="flex justify-center">
                    <button id="expand-export-button" 
                            class="bg-green-600 text-white py-2 px-6 rounded-lg font-medium hover:bg-green-700 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        下载扩展结果
                    </button>
                </div>
            </div>

            <!-- 错误显示 -->
            <div id="expand-error-section" class="bg-white rounded-lg shadow-md p-6 hidden">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">扩展失败</h3>
                    <p id="expand-error-message" class="text-gray-600 mb-4">发生了未知错误</p>
                    <button id="expand-retry-button" 
                            class="bg-orange-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-orange-700 transition-colors">
                        <i class="fas fa-redo mr-2"></i>
                        重新尝试
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-200 mt-12">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-600">
                    © 2024 SEM关键词智能分组工具 V2.0 - FastAPI版本
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-600">
                        <i class="fas fa-rocket mr-1"></i>
                        高性能异步处理
                    </span>
                </div>
            </div>
        </div>
    </footer>

    <!-- 加载Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    
    <!-- 主要JavaScript -->
    <script src="/static/script.js"></script>
</body>
</html> 